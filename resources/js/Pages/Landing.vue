<script setup>
import { ref, computed, watch } from "vue";
import { Head } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import axios from 'axios';
import { debounce } from 'lodash';

// --- Reactive State ---
const searchQuery = ref('');
const selectedLanguage = ref('en');
const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const showFilters = ref(false);
const hoveredResult = ref(null);

// Search results structure
const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'];
const searchResults = ref(
    Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]))
);

// Language options
const languages = [
    { code: 'rw', name: 'Kiny', flag: '🇷🇼' },
    { code: 'en', name: 'Eng', flag: '🇺🇸' },
    { code: 'fr', name: '<PERSON>a', flag: '🇫🇷' },
];

// Feature card data
const features = ref([
    {
        title: "Real-Time Tracking",
        description: "Track assets with high-precision GPS for instant location updates.",
        list: [
            "Low-latency updates",
            "High-precision tracking",
            "Real-time monitoring"
        ],
        image: "/images/tracking.jpg"
    },
    {
        title: "Geofencing Precision",
        description: "Set accurate boundaries using Rwanda’s administrative data.",
        list: [
            "Less than 1m accuracy",
            "Complete boundary data",
            "Customizable zones"
        ],
        image: "/images/geofencing.jpg"
    }
]);

// --- Computed Properties ---
const totalResults = computed(() => {
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const hasResults = computed(() => totalResults.value > 0);


const getPlaceholderText = computed(() => ({
    rw: 'Shakisha ahantu...',
    en: 'Search Rwanda locations...',
    fr: 'Rechercher des lieux...',
}[selectedLanguage.value] || 'Search locations...'));

// --- Functions ---
const performSearch = debounce(async (query, lang) => {
    if (query.trim().length < 2) {
        clearSearch(false);
        return;
    }
    
    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();
    
    try {
        const { data } = await axios.post('/map/search-json', {
            searchQuery: query.trim(),
            lang,
            filterData: 'all',
        });
        
        searchResults.value = Object.fromEntries(
            ALL_RESULT_TYPES.map(type => {
                const items = data[type] || [];
                return [
                    type,
                    items.map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }))
                ];
            })
        );
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch search results.';
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
    }
}, 300);

const clearSearch = (resetQuery = true) => {
    if (resetQuery) searchQuery.value = '';
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
    hoveredResult.value = null;
};

const getDisplayName = (result) => {
    return result[`name_${selectedLanguage.value}`] || result.name_en || result.name_local || result.name || 'N/A';
};

const getResultType = (type) => {
    return type.slice(0, -1); // Remove 's' from plural
};

const getHoverColor = (type) => {
    const colors = {
        provinces: '#1E88E5', // Blue
        districts: '#43A047', // Green
        sectors: '#FFB300', // Yellow
        cells: '#1E88E5',
        villages: '#43A047',
        healthFacs: '#FFB300'
    };
    return colors[type] || '#1E88E5';
};

// --- Watchers ---
watch(searchQuery, (newQuery) => {
    performSearch(newQuery, selectedLanguage.value);
});

watch(selectedLanguage, (newLang) => {
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value, newLang);
    }
});
</script>

<template>
    <Head title="Rwanda Geo - Developer First Geocoding API" />
    <AppLayout>
        <div class="bg-white min-h-screen">
            <!-- Section 1: Clean Search Section with Dynamic Rwanda Map Background -->
            <section class="py-12 relative overflow-hidden bg-gradient-to-br from-gray-50 to-white">
                <!-- Dynamic Rwanda Map Background -->
                <div class="absolute inset-0 opacity-5 pointer-events-none transition-all duration-500"
                     :style="{
                         backgroundImage: `url('/images/rwanda-map.svg')`,
                         backgroundSize: '80%',
                         backgroundPosition: 'center',
                         backgroundRepeat: 'no-repeat'
                     }">
                    <!-- Dynamic color overlay based on hovered result -->
                    <div v-if="hoveredResult"
                         class="absolute inset-0 transition-all duration-500 mix-blend-multiply"
                         :style="{
                             backgroundColor: getHoverColor(hoveredResult.type),
                             opacity: '0.15',
                             filter: 'blur(100px)'
                         }"></div>
                </div>

                <!-- Decorative elements -->
                <div class="absolute top-10 left-10 w-20 h-20 bg-blue-500 rounded-full opacity-10 animate-pulse"></div>
                <div class="absolute bottom-10 right-10 w-16 h-16 bg-green-500 rounded-full opacity-10 animate-pulse delay-1000"></div>
                <div class="absolute top-1/2 right-20 w-12 h-12 bg-yellow-500 rounded-full opacity-10 animate-pulse delay-500"></div>

                <div class="max-w-4xl mx-auto px-6 relative z-10">
                    <!-- Clean Title -->
                    <div class="text-center mb-12">
                        <h1 class="text-5xl font-bold text-gray-900 mb-4">
                            Rwanda <span class="text-blue-600">Geo</span>
                        </h1>
                        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                            Precise geocoding for Rwanda's administrative boundaries
                        </p>
                    </div>

                    <!-- Clean Search Bar -->
                    <div class="flex items-center gap-4 mb-8 max-w-2xl mx-auto">
                        <div class="relative flex-1">
                            <svg class="absolute left-5 top-1/2 -translate-y-1/2 h-6 w-6 text-gray-400"
                                 xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                            </svg>
                            <Input
                                v-model="searchQuery"
                                :placeholder="getPlaceholderText"
                                class="w-full pl-16 pr-16 py-5 text-lg rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-0 bg-white shadow-lg transition-all duration-300"
                            />
                            <div v-if="isLoading" class="absolute right-5 top-1/2 -translate-y-1/2">
                                <div class="animate-spin h-6 w-6 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                            </div>
                            <div v-else-if="searchQuery" class="absolute right-5 top-1/2 -translate-y-1/2">
                                <button @click="clearSearch()" class="text-gray-400 hover:text-gray-600 transition-colors">
                                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <Button @click="showFilters = !showFilters"
                                class="bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 rounded-full p-4 shadow-lg transition-all duration-300">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                            </svg>
                        </Button>
                    </div>

                    <!-- Filter Dropdown -->
                    <div v-if="showFilters" class="mb-8 bg-white border border-gray-200 rounded-2xl p-6 shadow-xl max-w-2xl mx-auto">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm font-semibold text-gray-700">Language Preferences</span>
                            <button @click="showFilters = false" class="text-gray-400 hover:text-gray-600 transition-colors">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        <div class="flex gap-3 justify-center">
                            <button
                                v-for="lang in languages"
                                :key="lang.code"
                                @click="selectedLanguage = lang.code"
                                :class="[
                                    'px-6 py-3 text-sm font-medium rounded-full transition-all duration-300 flex items-center gap-2 shadow-md',
                                    selectedLanguage === lang.code
                                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white transform scale-105'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                ]"
                            >
                                <span class="text-lg">{{ lang.flag }}</span>
                                {{ lang.name }}
                            </button>
                        </div>
                    </div>

                    <!-- Search Results -->
                    <div v-if="hasResults || isLoading || error" class="bg-white border border-gray-200 rounded-2xl shadow-xl max-w-2xl mx-auto">
                        <div class="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
                            <div class="text-sm text-gray-600 flex items-center justify-between">
                                <span v-if="isLoading" class="flex items-center gap-2">
                                    <div class="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                                    Searching...
                                </span>
                                <span v-else-if="error" class="text-red-600 font-medium">{{ error }}</span>
                                <span v-else class="font-medium">{{ totalResults }} result{{ totalResults !== 1 ? 's' : '' }}</span>
                                <span v-if="searchTime > 0 && !isLoading" class="text-xs bg-gradient-to-r from-blue-500 to-blue-600 text-white px-3 py-1 rounded-full">{{ searchTime }}ms</span>
                            </div>
                        </div>
                        <div class="max-h-80 overflow-y-auto">
                            <template v-for="type in ALL_RESULT_TYPES" :key="type">
                                <div v-for="result in searchResults[type]"
                                     :key="`${type}-${result.id}`"
                                     class="px-6 py-4 border-b border-gray-50 last:border-b-0 hover:bg-gradient-to-r hover:from-gray-50 hover:to-white transition-all duration-300 cursor-pointer"
                                     @mouseover="hoveredResult = { ...result, type }"
                                     @mouseleave="hoveredResult = null">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-4 h-4 rounded-full shadow-sm transition-all duration-300"
                                             :style="{ backgroundColor: getHoverColor(type) }"></div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-base font-semibold text-gray-900 truncate">
                                                {{ getDisplayName(result) }}
                                            </p>
                                            <div class="flex items-center space-x-3 mt-1">
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white capitalize shadow-sm"
                                                      :style="{ backgroundColor: getHoverColor(type) }">
                                                    {{ getResultType(type) }}
                                                </span>
                                                <p v-if="result.address" class="text-sm text-gray-500 truncate">
                                                    {{ result.address }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 2: Features Section -->
            <section class="py-20 bg-gradient-to-br from-white to-gray-50 relative overflow-hidden">
                <!-- Subtle background pattern -->
                <div class="absolute inset-0 opacity-5" style="background-image: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>

                <div class="max-w-7xl mx-auto px-6 relative z-10">
                    <!-- Left-aligned headers -->
                    <div class="text-left mb-16">
                        <h2 class="text-4xl font-bold text-gray-900 mb-4">
                            Core <span class="text-green-600">Features</span>
                        </h2>
                    <p class="text-xl text-gray-600 max-w-3xl">Discover the powerful tools designed for Rwanda’s unique geocoding needs.</p>
                    </div>

                    <!-- Feature Cards -->
                    <div class="space-y-16">
                        <div v-for="(feature, index) in features" :key="index"
                             class="bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                            <div class="flex flex-col lg:flex-row">
                                <!-- Image Section -->
                                <div class="lg:w-1/2 relative overflow-hidden">
                                    <img :src="feature.image" :alt="feature.title"
                                         class="w-full h-80 lg:h-full object-cover transition-transform duration-500 hover:scale-105">
                                    <div class="absolute inset-0 bg-gradient-to-r from-black/20 to-transparent"></div>
                                </div>

                                <!-- Content Section -->
                                <div class="lg:w-1/2 p-8 lg:p-12 flex flex-col justify-center">
                                    <h3 class="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-3">
                                        <div class="w-3 h-3 rounded-full"
                                             :style="{ backgroundColor: index === 0 ? '#3B82F6' : '#10B981' }"></div>
                                        {{ feature.title }}
                                    </h3>
                                    <p class="text-lg text-gray-600 mb-6 leading-relaxed">{{ feature.description }}</p>
                                    <ul class="space-y-3">
                                        <li v-for="(item, i) in feature.list" :key="i"
                                            class="flex items-center gap-3 text-gray-700">
                                            <div class="w-2 h-2 rounded-full"
                                                 :style="{ backgroundColor: '#FBBF24' }"></div>
                                            <span class="font-medium">{{ item }}</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </AppLayout>
</template>

<style scoped>
/* Animation keyframes */
@keyframes fade-in {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slide-in {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Animation classes */
.animate-fade-in {
    animation: fade-in 0.8s ease-out forwards;
}

.animate-slide-in {
    animation: slide-in 0.6s ease-out forwards;
    opacity: 0;
}

/* Custom scrollbar for results */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #1E88E5;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1565C0;
}
</style>