<script setup>
import { ref, computed, watch } from "vue";
import { Head, <PERSON> } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import axios from 'axios';
import { debounce } from 'lodash';

// --- Reactive State ---
const searchQuery = ref('');
const selectedLanguage = ref('en');
const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const showFilters = ref(false);
const hoveredResult = ref(null);

// Search results structure
const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'];
const searchResults = ref(
    Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]))
);

// Language options
const languages = [
    { code: 'rw', name: 'Kiny', flag: '🇷🇼' },
    { code: 'en', name: 'Eng', flag: '🇺🇸' },
    { code: 'fr', name: '<PERSON>a', flag: '🇫🇷' },
];

// Feature card data
const features = ref([
    {
        title: "Real-Time Tracking",
        description: "Track assets with high-precision GPS for instant location updates.",
        list: [
            "Low-latency updates",
            "High-precision tracking",
            "Real-time monitoring"
        ],
        image: "/images/tracking.jpg"
    },
    {
        title: "Geofencing Precision",
        description: "Set accurate boundaries using Rwanda’s administrative data.",
        list: [
            "Less than 1m accuracy",
            "Complete boundary data",
            "Customizable zones"
        ],
        image: "/images/geofencing.jpg"
    }
]);

// --- Computed Properties ---
const totalResults = computed(() => {
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const hasResults = computed(() => totalResults.value > 0);

const canSearch = computed(() => searchQuery.value.trim().length >= 2);

const getPlaceholderText = computed(() => ({
    rw: 'Shakisha ahantu...',
    en: 'Search Rwanda locations...',
    fr: 'Rechercher des lieux...',
}[selectedLanguage.value] || 'Search locations...'));

// --- Functions ---
const performSearch = debounce(async (query, lang) => {
    if (query.trim().length < 2) {
        clearSearch(false);
        return;
    }
    
    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();
    
    try {
        const { data } = await axios.post('/map/search-json', {
            searchQuery: query.trim(),
            lang,
            filterData: 'all',
        });
        
        searchResults.value = Object.fromEntries(
            ALL_RESULT_TYPES.map(type => {
                const items = data[type] || [];
                return [
                    type,
                    items.map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }))
                ];
            })
        );
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch search results.';
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
    }
}, 300);

const clearSearch = (resetQuery = true) => {
    if (resetQuery) searchQuery.value = '';
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
    hoveredResult.value = null;
};

const getDisplayName = (result) => {
    return result[`name_${selectedLanguage.value}`] || result.name_en || result.name_local || result.name || 'N/A';
};

const getResultType = (type) => {
    return type.slice(0, -1); // Remove 's' from plural
};

const getHoverColor = (type) => {
    const colors = {
        provinces: '#1E88E5', // Blue
        districts: '#43A047', // Green
        sectors: '#FFB300', // Yellow
        cells: '#1E88E5',
        villages: '#43A047',
        healthFacs: '#FFB300'
    };
    return colors[type] || '#1E88E5';
};

// --- Watchers ---
watch(searchQuery, (newQuery) => {
    performSearch(newQuery, selectedLanguage.value);
});

watch(selectedLanguage, (newLang) => {
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value, newLang);
    }
});
</script>

<template>
    <Head title="Rwanda Geo - Developer First Geocoding API" />
    <AppLayout>
        <div class="bg-gray-50 min-h-screen">
            <!-- Section 1: Search Section with Rwanda Map Background -->
            <section class="py-16 relative overflow-hidden">
                <!-- Dynamic Background with Rwanda Map -->
                <div class="absolute inset-0 opacity-20 pointer-events-none transition-opacity duration-300"
                     :style="{ backgroundImage: `url('/images/rwanda-map.svg')`, backgroundSize: 'cover', backgroundPosition: 'center' }">
                    <div v-if="hoveredResult" 
                         class="absolute inset-0 transition-opacity duration-300" 
                         :style="{ backgroundColor: getHoverColor(hoveredResult.type), opacity: '0.1' }"></div>
                </div>

                <div class="max-w-4xl mx-auto px-6 relative z-10">
                    <!-- Title and Search Bar -->
                    <h1 class="text-4xl font-bold text-black mb-8">Rwanda Geo Platform</h1>
                    <div class="flex items-center gap-4 mb-8">
                        <div class="relative flex-1">
                            <svg class="absolute left-4 top-1/2 -translate-y-1/2 h-6 w-6 text-gray-400" 
                                 xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                            </svg>
                            <Input 
                                v-model="searchQuery" 
                                :placeholder="getPlaceholderText" 
                                class="w-full pl-14 pr-14 py-4 text-lg rounded-2xl border-2 border-gray-200 focus:border-blue-500 focus:ring-0 bg-white"
                            />
                            <div v-if="isLoading" class="absolute right-4 top-1/2 -translate-y-1/2">
                                <div class="animate-spin h-6 w-6 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                            </div>
                            <div v-else-if="searchQuery" class="absolute right-4 top-1/2 -translate-y-1/2">
                                <button @click="clearSearch()" class="text-gray-400 hover:text-gray-600">
                                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <Button @click="showFilters = !showFilters" class="bg-blue-500 text-white hover:bg-blue-600 rounded-full p-3">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1m-1 4H5m10 4H5m6 4h10" />
                            </svg>
                        </Button>
                    </div>

                    <!-- Filter Dropdown -->
                    <div v-if="showFilters" class="mb-8 bg-white border-2 border-gray-200 rounded-2xl p-4 shadow-lg">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm font-medium text-gray-600">Filter by Language</span>
                            <button @click="showFilters = false" class="text-gray-400 hover:text-gray-600">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        <div class="flex gap-2">
                            <button
                                v-for="lang in languages"
                                :key="lang.code"
                                @click="selectedLanguage = lang.code"
                                :class="[
                                    'px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 flex items-center gap-2',
                                    selectedLanguage === lang.code 
                                        ? 'bg-blue-500 text-white' 
                                        : 'text-gray-600 hover:bg-gray-100'
                                ]"
                            >
                                <span>{{ lang.flag }}</span>
                                {{ lang.name }}
                            </button>
                        </div>
                    </div>

                    <!-- Search Results -->
                    <div v-if="hasResults || isLoading || error" class="bg-white border-2 border-gray-200 rounded-2xl shadow-lg">
                        <div class="px-6 py-3 border-b border-gray-100 bg-gray-50">
                            <div class="text-sm text-gray-600 flex items-center justify-between">
                                <span v-if="isLoading">Searching...</span>
                                <span v-else-if="error" class="text-red-600">{{ error }}</span>
                                <span v-else>{{ totalResults }} result{{ totalResults !== 1 ? 's' : '' }}</span>
                                <span v-if="searchTime > 0 && !isLoading" class="text-xs bg-blue-500 text-white px-2 py-1 rounded-full">{{ searchTime }}ms</span>
                            </div>
                        </div>
                        <div class="max-h-80 overflow-y-auto">
                            <template v-for="type in ALL_RESULT_TYPES" :key="type">
                                <div v-for="(result, index) in searchResults[type]" 
                                     :key="`${type}-${result.id}`" 
                                     class="px-6 py-4 border-b border-gray-50 last:border-b-0 hover:bg-gray-50 transition-colors duration-150"
                                     @mouseover="hoveredResult = { ...result, type }"
                                     @mouseleave="hoveredResult = null">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 rounded-full" 
                                             :style="{ backgroundColor: getHoverColor(type) }"></div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-base font-medium text-gray-900 truncate">
                                                {{ getDisplayName(result) }}
                                            </p>
                                            <div class="flex items-center space-x-3 mt-1">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 capitalize">
                                                    {{ getResultType(type) }}
                                                </span>
                                                <p v-if="result.address" class="text-sm text-gray-500 truncate">
                                                    {{ result.address }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 2: Features Section -->
            <section class="py-16 bg-white">
                <div class="max-w-6xl mx-auto px-6">
                    <h2 class="text-3xl font-bold text-black mb-4">Our Core Features</h2>
                    <p class="text-lg text-gray-600 mb-12 max-w-2xl">Discover the powerful tools designed for Rwanda’s unique geocoding needs.</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div v-for="(feature, index) in features" :key="index" class="flex flex-col md:flex-row gap-6">
                            <div class="md:w-1/2">
                                <img :src="feature.image" :alt="feature.title" class="w-full h-64 object-cover rounded-2xl">
                            </div>
                            <div class="md:w-1/2">
                                <h3 class="text-xl font-bold text-black mb-2">{{ feature.title }}</h3>
                                <p class="text-gray-600 mb-4">{{ feature.description }}</p>
                                <ul class="list-disc pl-5 text-gray-600">
                                    <li v-for="(item, i) in feature.list" :key="i">{{ item }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </AppLayout>
</template>

<style scoped>
/* Animation keyframes */
@keyframes fade-in {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slide-in {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Animation classes */
.animate-fade-in {
    animation: fade-in 0.8s ease-out forwards;
}

.animate-slide-in {
    animation: slide-in 0.6s ease-out forwards;
    opacity: 0;
}

/* Custom scrollbar for results */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #1E88E5;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1565C0;
}
</style>