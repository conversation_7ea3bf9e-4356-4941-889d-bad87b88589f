<?php

namespace App\Services;

use App\Data\CustomDataDTO;
use App\Data\DataMapData;
use App\Data\DataMapItemData;
use App\Enums\MapTypeEnums;
use App\Exceptions\NotFound;
use App\Exceptions\Forbidden;
use App\Models\DataMap;
use App\Models\DataMapItem;
use App\Models\PlaceMap;
use App\Models\PlaceMapItem;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DataMapService
{

    public function createDataMap(int $userId, int $placeMapId, DataMapData $data)
    {


        $placeMap = PlaceMap::where('id', $placeMapId)->where('user_id', $userId)->first();

        throw_if(is_null($placeMap), NotFound::class, 'Place map not found');

        switch ($placeMap->type) {
            case MapTypeEnums::TRACKING->value:
                $customfields = config('geo-data.gps-field');
                break;
            case MapTypeEnums::ITENARY->value:
                $customfields = config('geo-data.itenary');
                break;
            default:
                $customfields = [];
        }

        DataMap::create([
            'user_id' => $userId,
            'image' => $data->image,
            'name' => $data->name,
            'description' => $data->description,
            'visibility' => $data->visibility,
            'status' => $data->status,
            'place_map_id' => $placeMapId,
            'customFields' => $customfields
        ]);
    }

    public function updateDataMap(int $userId, int $placeMapId, int $dataMapId, DataMapData $data)
    {

        $dataMap = DataMap::where('id', $dataMapId)
            ->where('place_map_id', $placeMapId)
            ->where('user_id', $userId)
            ->first();

        throw_if(is_null($dataMap), NotFound::class, 'Data map not found');

        $dataMap->update([
            'name' => $data->name,
            'image' => $data->image,
            'description' => $data->description,
            'visibility' => $data->visibility,
            'status' => $data->status,
            'customFields' => $data->customFields,
        ]);
    }

    public function getDataMapById(int $userId, int $placeMapId, int $dataMapId)
    {

        $dataMap = DataMap::where('id', $dataMapId)
            ->where('place_map_id', $placeMapId)
            ->where('user_id', $userId)
            ->first();

        throw_if(is_null($dataMap), NotFound::class, 'Data map not found');

        return $dataMap;
    }

    public function getDataMap(int $userId, int $placeMapId, int $perPage, int $page, ?string $searchQuery): LengthAwarePaginator
    {

        $placeMap = PlaceMap::where('id', $placeMapId)->where('user_id', $userId)->first();

        throw_if(is_null($placeMap), NotFound::class, 'Place map not found');


        if (is_null($searchQuery)) {

            return DataMap::where('place_map_id', $placeMapId)
                ->where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->paginate(perPage: $perPage, page: $page);
        }

        return DataMap::search($searchQuery)
            ->where('place_map_id', $placeMapId)
            ->where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->paginate(perPage: $perPage, page: $page);
    }

    public function createDataMapItem(int $userId, int $dataMapId, DataMapItemData $data)
    {

        $dataMap = DataMap::where('id', $dataMapId)
            ->where('user_id', $userId)
            ->first();

        throw_if(is_null($dataMap), NotFound::class, 'Data map not found');

        $validatePlacemap = PlaceMapItem::where('id', $data->placeMapItemID)
            ->where('place_map_id', $dataMap->place_map_id)
            ->first();

        throw_if(is_null($validatePlacemap), NotFound::class, 'Place map item not found');

        // todo

        return DataMapItem::create([
            'data_map_id' => $dataMapId,
            'name' => $data->name,
            'description' => $data->description,
            'visibility' => $data->visibility,
            'status' => $data->status,
            'details' => $data->customFields,
            'place_map_item_id' => $data->placeMapItemID,
            'dataItems' => $data->dataItems,
            'type' => $data->type,
        ]);
    }

    public function updateDataMapItem(int $userId, int $dataMapId, int $dataMapItemId, DataMapItemData $data)
    {

        $dataMap = DataMap::where('id', $dataMapId)
            ->where('user_id', $userId)
            ->first();

        throw_if(is_null($dataMap), NotFound::class, 'Data map not found');

        $validatePlacemap = PlaceMapItem::where('id', $data->placeMapItemID)
            ->where('place_map_id', $dataMap->place_map_id)
            ->first();

        throw_if(is_null($validatePlacemap), NotFound::class, 'Place map item not found');

        $dataMapItem = DataMapItem::where('id', $dataMapItemId)
            ->where('data_map_id', $dataMapId)
            ->first();

        throw_if(is_null($dataMapItem), NotFound::class, 'Data map item not found');

        // todo

        $dataMapItem->update([
            'name' => $data->name,
            'description' => $data->description,
            'visibility' => $data->visibility,
            'status' => $data->status,
            'details' => $data->customFields,
            'place_map_item_id' => $data->placeMapItemID,
            'dataItems' => $data->dataItems,
            'type' => $data->type,
        ]);
    }

    public function getDataMapItem(int $userId, int $dataMapId, int $perPage, int $page, ?string $searchQuery): LengthAwarePaginator
    {

        $dataMap = DataMap::where('id', $dataMapId)
            ->where('user_id', $userId)
            ->first();

        throw_if(is_null($dataMap), NotFound::class, 'Data map not found');

        if (is_null($searchQuery)) {

            return DataMapItem::where('data_map_id', $dataMapId)
                ->orderBy('created_at', 'desc')
                ->paginate(perPage: $perPage, page: $page);
        }

        return DataMapItem::search($searchQuery)
            ->where('data_map_id', $dataMapId)
            ->orderBy('created_at', 'desc')
            ->paginate(perPage: $perPage, page: $page);
    }

    /**
     * Create a new custom data item
     */
    public function createCustomData(int $userId, CustomDataDTO $newDataItem)
    {
        $newDataItemArray = $newDataItem->toArray();
        $dataMapItemId = $newDataItemArray['dataMapItemID'];
        $dataItems = $newDataItemArray['dataItems'];

        $dataMapItem = DataMapItem::whereHas('dataMap', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('id', $dataMapItemId)->first();

        throw_if(is_null($dataMapItem), NotFound::class, 'Data map item not found');

        $dataMap = $dataMapItem->dataMap;
        $validationErrors = $this->validateCustomData($dataMap->customFields, $dataItems);

        if (!empty($validationErrors)) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $validationErrors));
        }

        $existingItems = is_array($dataMapItem->dataItems) ? $dataMapItem->dataItems : [];
        $newSubArray = [
            'id' => $this->generateDataItemId($existingItems),
            'timestamp' => now()->toISOString(),
        ];

        foreach ($dataItems as $item) {
            $newSubArray[$item['name']] = $item['value'];
        }

        $existingItems[] = $newSubArray; // Append new sub-array to existing items

        $dataMapItem->update([
            'dataItems' => $existingItems
        ]);

        return true;
    }
    /**
     * Remove a data item from the dataItems JSON field by ID
     */
    public function removeDataItem(int $userId, int $dataMapId, int $dataMapItemId, string $objectId): bool
    {
        $dataMapItem = DataMapItem::where('id', $dataMapItemId)
            ->where('data_map_id', $dataMapId)
            ->whereHas('dataMap', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->first();

        throw_if(is_null($dataMapItem), NotFound::class, 'Data map item not found');

        $currentItems = is_array($dataMapItem->dataItems) ? $dataMapItem->dataItems : [];
        $itemIndex = null;

        foreach ($currentItems as $index => $item) {
            if (isset($item['id']) && $item['id'] === $objectId) {
                $itemIndex = $index;
                break;
            }
        }

        if ($itemIndex === null) {
            throw new NotFound('Data item with ID ' . $objectId . ' not found');
        }

        unset($currentItems[$itemIndex]);
        $currentItems = array_values($currentItems);

        $dataMapItem->update([
            'dataItems' => $currentItems
        ]);

        return true;
    }


    public function updateDataItem(int $userId, int $dataMapItemId, string $itemId, CustomDataDTO $updatedData): bool
    {
        $updatedDataArray = $updatedData->toArray();
        $dataMapItemId = $updatedDataArray['dataMapItemID'];
        $dataItems = $updatedDataArray['dataItems'];

        $dataMapItem = DataMapItem::whereHas('dataMap', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('id', $dataMapItemId)->first();

        throw_if(is_null($dataMapItem), NotFound::class, 'Data map item not found');

        $dataMap = $dataMapItem->dataMap;
        $validationErrors = $this->validateCustomData($dataMap->customFields, $dataItems);

        if (!empty($validationErrors)) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $validationErrors));
        }

        $currentItems = is_array($dataMapItem->dataItems) ? $dataMapItem->dataItems : [];
        $itemIndex = null;

        foreach ($currentItems as $index => $subArray) {
            if (isset($subArray['id']) && $subArray['id'] === $itemId) {
                $itemIndex = $index;
                break;
            }
        }

        if ($itemIndex === null) {
            throw new NotFound('Data item with ID ' . $itemId . ' not found');
        }

        $newSubArray = [
            'id' => $itemId,
            'timestamp' => now()->toISOString(),
        ];

        foreach ($dataItems as $item) {
            $newSubArray[$item['name']] = $item['value'];
        }

        $currentItems[$itemIndex] = $newSubArray;

        $dataMapItem->update([
            'dataItems' => $currentItems
        ]);

        return true;
    }

    public function getCustomData(int $userId, int $perPage, int $page, int $dataMapId, int $dataMapItemId): LengthAwarePaginator
    {
        $dataMapItem = DataMapItem::where('id', $dataMapItemId)
            ->where('data_map_id', $dataMapId)
            ->whereHas('dataMap', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->first();

        throw_if(is_null($dataMapItem), NotFound::class, 'Data map item not found');

        $items = is_array($dataMapItem->dataItems) ? $dataMapItem->dataItems : [];

        // Ensure all items have an ID for stability
        $items = array_map(function ($item, $index) {
            if (!isset($item['id'])) {
                $item['id'] = 'item_' . $index . '_' . uniqid();
            }
            return $item;
        }, $items, array_keys($items));

        // Sort by timestamp descending
        usort($items, function ($a, $b) {
            $aTime = $a['timestamp'] ?? 0;
            $bTime = $b['timestamp'] ?? 0;
            return strcmp($bTime, $aTime);
        });

        $totalItems = count($items);
        $offset = ($page - 1) * $perPage;
        $paginatedItems = array_slice($items, $offset, $perPage);

        return new LengthAwarePaginator(
            $paginatedItems,
            $totalItems,
            $perPage,
            $page,
            [
                'path' => request()->url(),
                'pageName' => 'page'
            ]
        );
    }

    /**
     * Get a specific data item from the dataItems JSON field by ID
     */
    public function getDataItem(int $userId, int $dataMapItemId, string $itemId): array
    {
        $dataMapItem = DataMapItem::whereHas('dataMap', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('id', $dataMapItemId)->first();

        throw_if(is_null($dataMapItem), NotFound::class, 'Data map item not found');

        $currentItems = is_array($dataMapItem->dataItems) ? $dataMapItem->dataItems : [];

        foreach ($currentItems as $item) {
            if (isset($item['id']) && $item['id'] === $itemId) {
                return [
                    'id' => $item['id'],
                    'name' => $item['name'] ?? null,
                    'value' => $item['value'] ?? null,
                    'created_at' => $item['created_at'] ?? null,
                    'updated_at' => $item['updated_at'] ?? null,
                ];
            }
        }

        throw new NotFound('Data item with ID ' . $itemId . ' not found');
    }

    /**
     * Get all data items from the dataItems JSON field with pagination and optional filtering
     */
    public function getAllDataItems(
        int $userId,
        int $dataMapItemId,
        int $perPage = 15,
        int $page = 1,
        ?array $filters = null,
        ?string $searchQuery = null
    ): LengthAwarePaginator {
        $dataMapItem = DataMapItem::whereHas('dataMap', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('id', $dataMapItemId)->first();

        throw_if(is_null($dataMapItem), NotFound::class, 'Data map item not found');

        $items = is_array($dataMapItem->dataItems) ? $dataMapItem->dataItems : [];

        $items = array_map(function ($item, $index) {
            if (!isset($item['id'])) {
                $item['id'] = 'legacy_item_' . $index . '_' . time();
            }
            return $item;
        }, $items, array_keys($items));

        if ($searchQuery) {
            $items = array_filter($items, function ($item) use ($searchQuery) {
                $searchTerm = strtolower($searchQuery);
                $nameMatch = isset($item['name']) && strpos(strtolower($item['name']), $searchTerm) !== false;
                $valueMatch = isset($item['value']) && strpos(strtolower((string)$item['value']), $searchTerm) !== false;
                $idMatch = isset($item['id']) && strpos(strtolower($item['id']), $searchTerm) !== false;
                return $nameMatch || $valueMatch || $idMatch;
            });
        }

        if ($filters) {
            $items = array_filter($items, function ($item) use ($filters) {
                foreach ($filters as $key => $value) {
                    if (!isset($item[$key]) || $item[$key] != $value) {
                        return false;
                    }
                }
                return true;
            });
        }

        $items = array_values($items);
        usort($items, function ($a, $b) {
            $aTime = $a['created_at'] ?? '1970-01-01T00:00:00.000Z';
            $bTime = $b['created_at'] ?? '1970-01-01T00:00:00.000Z';
            return strcmp($bTime, $aTime);
        });

        $totalItems = count($items);
        $offset = ($page - 1) * $perPage;
        $paginatedItems = array_slice($items, $offset, $perPage);

        $paginatedItems = array_map(function ($item) {
            return [
                'id' => $item['id'],
                'name' => $item['name'] ?? null,
                'value' => $item['value'] ?? null,
                'created_at' => $item['created_at'] ?? null,
                'updated_at' => $item['updated_at'] ?? null,
            ];
        }, $paginatedItems);

        return new LengthAwarePaginator(
            $paginatedItems,
            $totalItems,
            $perPage,
            $page,
            [
                'path' => request()->url(),
                'pageName' => 'page'
            ]
        );
    }

    /**
     * Advanced search with pagination using database-level JSON queries
     */
    public function searchDataItemsWithPagination(
        int $userId,
        int $dataMapItemId,
        ?string $searchQuery = null,
        ?array $filters = null,
        int $perPage = 15,
        int $page = 1
    ): LengthAwarePaginator {
        $query = DataMapItem::whereHas('dataMap', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        })->where('id', $dataMapItemId);

        if ($searchQuery) {
            $query->where(function ($q) use ($searchQuery) {
                $q->whereRaw("JSON_SEARCH(dataItems, 'all', ?, NULL, '$[*].name') IS NOT NULL", ["%{$searchQuery}%"])
                    ->orWhereRaw("JSON_SEARCH(dataItems, 'all', ?, NULL, '$[*].value') IS NOT NULL", ["%{$searchQuery}%"]);
            });
        }

        if ($filters) {
            foreach ($filters as $key => $value) {
                $query->whereRaw("JSON_SEARCH(dataItems, 'all', ?, NULL, '$[*].{$key}') IS NOT NULL", [$value]);
            }
        }

        $dataMapItem = $query->first();
        throw_if(is_null($dataMapItem), NotFound::class, 'Data map item not found or no matching data');

        return $this->getAllDataItems($userId, $dataMapItemId, $perPage, $page, $filters, $searchQuery);
    }

    /**
     * Generate a unique ID for new data items
     */
    private function generateDataItemId(?array $existingItems): string
    {
        $existingIds = [];
        if ($existingItems) {
            foreach ($existingItems as $item) {
                if (isset($item['id'])) {
                    $existingIds[] = $item['id'];
                }
            }
        }

        do {
            $newId = 'item_' . uniqid() . '_' . time();
        } while (in_array($newId, $existingIds));

        return $newId;
    }

    /**
     * Get statistics about data items in the JSON field
     */
    public function getDataItemsStats(int $userId, int $dataMapItemId): array
    {
        $dataMapItem = DataMapItem::whereHas('dataMap', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('id', $dataMapItemId)->first();

        throw_if(is_null($dataMapItem), NotFound::class, 'Data map item not found');

        $items = is_array($dataMapItem->dataItems) ? $dataMapItem->dataItems : [];
        $stats = [
            'total_items' => count($items),
            'field_counts' => [],
            'created_today' => 0,
            'created_this_week' => 0,
            'created_this_month' => 0,
        ];

        $today = now()->startOfDay();
        $weekStart = now()->startOfWeek();
        $monthStart = now()->startOfMonth();

        foreach ($items as $item) {
            if (isset($item['name'])) {
                $fieldName = $item['name'];
                $stats['field_counts'][$fieldName] = ($stats['field_counts'][$fieldName] ?? 0) + 1;
            }

            if (isset($item['created_at'])) {
                $createdAt = \Carbon\Carbon::parse($item['created_at']);
                if ($createdAt->gte($today)) {
                    $stats['created_today']++;
                }
                if ($createdAt->gte($weekStart)) {
                    $stats['created_this_week']++;
                }
                if ($createdAt->gte($monthStart)) {
                    $stats['created_this_month']++;
                }
            }
        }

        return $stats;
    }

    /**
     * Bulk operations: Add multiple data items at once
     */
    public function bulkAddDataItems(int $userId, int $dataMapItemId, array $newDataItems): bool
    {
        $dataMapItem = DataMapItem::whereHas('dataMap', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('id', $dataMapItemId)->first();

        throw_if(is_null($dataMapItem), NotFound::class, 'Data map item not found');

        $dataMap = $dataMapItem->dataMap;
        $existingItems = is_array($dataMapItem->dataItems) ? $dataMapItem->dataItems : [];
        $newItems = [];

        foreach ($newDataItems as $index => $item) {
            $validationErrors = $this->validateCustomData($dataMap->customFields, [$item]);
            if (!empty($validationErrors)) {
                throw new \InvalidArgumentException("Validation failed for item at index {$index}: " . implode(', ', $validationErrors));
            }

            $newItems[] = [
                'name' => $item['name'],
                'value' => $item['value'],
                'id' => $this->generateDataItemId($existingItems),
                'created_at' => now()->toISOString(),
            ];
            $existingItems[] = $newItems[count($newItems) - 1];
        }

        $dataMapItem->update([
            'dataItems' => array_merge($existingItems, $newItems)
        ]);

        return true;
    }

    /**
     * Bulk delete multiple data items by IDs
     */
    public function bulkDeleteDataItems(int $userId, int $dataMapItemId, array $itemIds): bool
    {
        $dataMapItem = DataMapItem::whereHas('dataMap', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('id', $dataMapItemId)->first();

        throw_if(is_null($dataMapItem), NotFound::class, 'Data map item not found');

        $currentItems = is_array($dataMapItem->dataItems) ? $dataMapItem->dataItems : [];
        $remainingItems = array_filter($currentItems, function ($item) use ($itemIds) {
            return !isset($item['id']) || !in_array($item['id'], $itemIds);
        });

        $remainingItems = array_values($remainingItems);
        $dataMapItem->update([
            'dataItems' => $remainingItems
        ]);

        return true;
    }

    /**
     * Validate custom data against customFields configuration
     */
    private function validateCustomData($customFields, array $dataItems): array
    {
        $errors = [];
        $submittedFields = [];

        foreach ($dataItems as $item) {
            if (!isset($item['name']) || !isset($item['value'])) {
                $errors[] = "Each data item must have 'name' and 'value' properties";
                continue;
            }
            $submittedFields[$item['name']] = $item['value'];
        }

        if (is_string($customFields)) {
            $customFields = json_decode($customFields, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errors[] = "Invalid custom fields configuration";
                return $errors;
            }
        }

        if (!is_array($customFields)) {
            $errors[] = "Custom fields must be an array";
            return $errors;
        }

        $customFields = array_filter($customFields, function ($field) {
            return is_array($field) && !empty($field['name']);
        });

        foreach ($customFields as $field) {
            $fieldName = $field['name'] ?? null;
            $fieldType = $field['type'] ?? 'text';
            $isRequired = $field['required'] ?? false;
            $maxLength = $field['length'] ?? 255;

            if (!$fieldName) {
                $errors[] = "Field configuration is missing name";
                continue;
            }

            if ($isRequired && !array_key_exists($fieldName, $submittedFields)) {
                $errors[] = "Required field '{$fieldName}' is missing";
                continue;
            }

            if (!array_key_exists($fieldName, $submittedFields)) {
                continue;
            }

            $value = $submittedFields[$fieldName];

            switch ($fieldType) {
                case 'text':
                    if (!is_string($value)) {
                        $errors[] = "Field '{$fieldName}' must be a string";
                    } elseif (strlen($value) > $maxLength) {
                        $errors[] = "Field '{$fieldName}' exceeds maximum length of {$maxLength} characters";
                    } elseif ($isRequired && empty(trim($value))) {
                        $errors[] = "Required field '{$fieldName}' cannot be empty";
                    }
                    break;

                case 'number':
                case 'numeric':
                    if (!is_numeric($value)) {
                        $errors[] = "Field '{$fieldName}' must be a number";
                    } elseif (strlen((string)$value) > $maxLength) {
                        $errors[] = "Field '{$fieldName}' exceeds maximum length of {$maxLength} digits";
                    }
                    break;

                case 'decimal':
                case 'float':
                    if (!is_numeric($value)) {
                        $errors[] = "Field '{$fieldName}' must be a decimal number";
                    } elseif (strlen((string)$value) > $maxLength) {
                        $errors[] = "Field '{$fieldName}' exceeds maximum length of {$maxLength} characters";
                    }
                    break;

                case 'date':
                    if (!strtotime($value)) {
                        $errors[] = "Field '{$fieldName}' must be a valid date";
                    }
                    break;

                case 'timestamp':
                    if (!is_numeric($value) && !strtotime($value)) {
                        $errors[] = "Field '{$fieldName}' must be a valid timestamp";
                    }
                    break;

                default:
                    if (!is_string($value)) {
                        $errors[] = "Field '{$fieldName}' must be a string";
                    } elseif (strlen($value) > $maxLength) {
                        $errors[] = "Field '{$fieldName}' exceeds maximum length of {$maxLength} characters";
                    }
            }

            if ($fieldName === 'longitude') {
                $longitude = floatval($value);
                if ($longitude < -180 || $longitude > 180) {
                    $errors[] = "Longitude must be between -180 and 180 degrees";
                }
            }

            if ($fieldName === 'latitude') {
                $latitude = floatval($value);
                if ($latitude < -90 || $latitude > 90) {
                    $errors[] = "Latitude must be between -90 and 90 degrees";
                }
            }
        }

        $definedFields = array_column($customFields, 'name');
        $extraFields = array_diff(array_keys($submittedFields), $definedFields);

        foreach ($extraFields as $extraField) {
            $errors[] = "Field '{$extraField}' is not defined in the custom fields configuration";
        }

        return $errors;
    }
}
