<?php

return [
    'gps-field' => [
        [
            'name' => 'Status',
            'length' => 30,
            'isRequired' => 'yes',
            'type' => 'text',
            'default' => 'yes'
        ],
        [
            'name' => 'Name',
            'length' => 50,
            'isRequired' => 'yes',
            'type' => 'text',
            'default' => 'yes'
        ],
        [
            'name' => 'Serial-Number',
            'length' => 50,
            'isRequired' => 'yes',
            'type' => 'text',
            'default' => 'yes'
        ],
        [
            'name' => 'Direction',
            'type' => 'text',
            'length' => 10,
            'isRequired' => 'no',
            'default' => 'yes'
        ],
        [
            'name' => 'Timestamp',
            'length' => 30,
            'isRequired' => 'yes',
            'type' => 'date',
            'default' => 'yes'
        ],
        [
            'name' => 'Latitude',
            'length' => null,
            'isRequired' => 'yes',
            'type' => 'number',
            'default' => 'yes'
        ],
        [
            'name' => 'Longitude',
            'length' => null,
            'isRequired' => 'yes',
            'type' => 'number',
            'default' => 'yes'
        ],
        [
            'name' => 'Details',
            'length' => 255,
            'isRequired' => 'no',
            'type' => 'text',
            'default' => 'yes'
        ]
    ],

    'geo-fancing' => [
        [
            'name' => 'Status',
            'type' => 'string',
            'length' => 30,
            'isRequired' => 'yes',
            'type' => 'text',
            'default' => 'yes'
        ],
        [
            'name' => 'Name',
            'type' => 'string',
            'length' => 50,
            'isRequired' => 'yes',
            'type' => 'text',
            'default' => 'yes'
        ],
        [
            'name' => 'ID',
            'type' => 'string',
            'length' => 36,
            'isRequired' => 'no',
            'type' => 'number',
            'default' => 'yes'
        ],
        [
            'name' => 'ID-Type',
            'type' => 'string',
            'length' => 30,
            'isRequired' => 'no',
            'type' => 'text',
            'default' => 'yes'
        ],
        [
            'name' => 'Polygon',
            'type' => 'string',
            'length' => null,
            'isRequired' => 'no',
            'type' => 'text',
            'default' => 'yes'
        ],
        [
            'name' => 'Action-Trigger',
            'type' => 'string',
            'length' => 100,
            'isRequired' => 'no',
            'type' => 'text',
            'default' => 'yes'
        ],
        [
            'name' => 'Details',
            'type' => 'string',
            'length' => 255,
            'isRequired' => 'no',
            'type' => 'text',
            'default' => 'yes'
        ]
    ],

    'itenary' => [
        [
            'name' => 'Near-By',
            'type' => 'string',
            'length' => 100,
            'isRequired' => 'yes',
            'type' => 'text',
            'default' => 'yes',
            'type' => 'subItems',
            'additialField' => [
                ['name' => 'title'],
                ['name' => 'distance']
            ]
        ],
        [
            'name' => 'To-Do',
            'type' => 'array',
            'length' => null,
            'isRequired' => 'no',
            'default' => 'yes',
            'type' => 'subItems',
            'additialField' => [
                ['name' => 'title'],
                ['name' => 'description']
            ]
        ]
    ]
];
